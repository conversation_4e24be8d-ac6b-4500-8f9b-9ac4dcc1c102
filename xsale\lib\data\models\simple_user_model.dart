class SimpleUserModel {
  final String id;
  final String email;
  final String name;
  final String? phone;
  final String? avatar;
  final bool isVerified;
  final DateTime createdAt;

  const SimpleUserModel({
    required this.id,
    required this.email,
    required this.name,
    this.phone,
    this.avatar,
    required this.isVerified,
    required this.createdAt,
  });

  String get displayName => name.isNotEmpty ? name : email.split('@').first;

  factory SimpleUserModel.fromJson(Map<String, dynamic> json) {
    return SimpleUserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String,
      phone: json['phone'] as String?,
      avatar: json['avatar'] as String?,
      isVerified: json['isVerified'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'phone': phone,
      'avatar': avatar,
      'isVerified': isVerified,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  SimpleUserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? phone,
    String? avatar,
    bool? isVerified,
    DateTime? createdAt,
  }) {
    return SimpleUserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class SimpleCategoryModel {
  final String id;
  final String nameAr;
  final String nameEn;
  final String icon;
  final String color;
  final List<SimpleCategoryModel> subcategories;

  const SimpleCategoryModel({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.icon,
    required this.color,
    this.subcategories = const [],
  });

  String getName(String locale) {
    return locale == 'ar' ? nameAr : nameEn;
  }

  factory SimpleCategoryModel.fromJson(Map<String, dynamic> json) {
    return SimpleCategoryModel(
      id: json['id'] as String,
      nameAr: json['nameAr'] as String,
      nameEn: json['nameEn'] as String,
      icon: json['icon'] as String,
      color: json['color'] as String,
      subcategories: (json['subcategories'] as List<dynamic>?)
          ?.map((e) => SimpleCategoryModel.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nameAr': nameAr,
      'nameEn': nameEn,
      'icon': icon,
      'color': color,
      'subcategories': subcategories.map((e) => e.toJson()).toList(),
    };
  }
}
