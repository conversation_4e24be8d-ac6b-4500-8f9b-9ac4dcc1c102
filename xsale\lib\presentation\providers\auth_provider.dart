import 'package:flutter/foundation.dart';
import '../../data/models/simple_user_model.dart';
import '../../data/services/auth_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  
  SimpleUserModel? _user;
  bool _isLoading = false;
  String? _error;

  SimpleUserModel? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _user != null;

  Future<void> initialize() async {
    await _authService.initialize();
    _user = _authService.currentUser;
    notifyListeners();
  }

  Future<bool> login({
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    final result = await _authService.login(
      email: email,
      password: password,
    );

    if (result.isSuccess) {
      _user = result.user;
      _setLoading(false);
      notifyListeners();
      return true;
    } else {
      _setError(result.error!);
      _setLoading(false);
      return false;
    }
  }

  Future<bool> register({
    required String name,
    required String email,
    required String password,
    String? phone,
  }) async {
    _setLoading(true);
    _clearError();

    final result = await _authService.register(
      name: name,
      email: email,
      password: password,
      phone: phone,
    );

    if (result.isSuccess) {
      _user = result.user;
      _setLoading(false);
      notifyListeners();
      return true;
    } else {
      _setError(result.error!);
      _setLoading(false);
      return false;
    }
  }

  Future<bool> loginWithPhone({
    required String phone,
    required String otp,
  }) async {
    _setLoading(true);
    _clearError();

    final result = await _authService.loginWithPhone(
      phone: phone,
      otp: otp,
    );

    if (result.isSuccess) {
      _user = result.user;
      _setLoading(false);
      notifyListeners();
      return true;
    } else {
      _setError(result.error!);
      _setLoading(false);
      return false;
    }
  }

  Future<bool> sendOTP(String phone) async {
    _setLoading(true);
    _clearError();

    final success = await _authService.sendOTP(phone);
    
    _setLoading(false);
    
    if (!success) {
      _setError('فشل في إرسال رمز التحقق');
    }
    
    return success;
  }

  Future<bool> resetPassword(String email) async {
    _setLoading(true);
    _clearError();

    final result = await _authService.resetPassword(email: email);
    
    _setLoading(false);
    
    if (!result.isSuccess) {
      _setError(result.error!);
    }
    
    return result.isSuccess;
  }

  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    _setLoading(true);
    _clearError();

    final result = await _authService.changePassword(
      currentPassword: currentPassword,
      newPassword: newPassword,
    );
    
    _setLoading(false);
    
    if (!result.isSuccess) {
      _setError(result.error!);
    }
    
    return result.isSuccess;
  }

  Future<bool> updateProfile({
    String? name,
    String? phone,
    String? bio,
    String? location,
  }) async {
    _setLoading(true);
    _clearError();

    final result = await _authService.updateProfile(
      name: name,
      phone: phone,
      bio: bio,
      location: location,
    );

    if (result.isSuccess) {
      _user = result.user;
      _setLoading(false);
      notifyListeners();
      return true;
    } else {
      _setError(result.error!);
      _setLoading(false);
      return false;
    }
  }

  Future<void> logout() async {
    _setLoading(true);
    
    await _authService.logout();
    
    _user = null;
    _setLoading(false);
    _clearError();
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
