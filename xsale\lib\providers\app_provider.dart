import 'package:flutter/material.dart';
import '../models/ad_model.dart';

class AppProvider with ChangeNotifier {
  // قائمة الإعلانات
  List<AdModel> _ads = [];
  List<AdModel> _favoriteAds = [];
  List<AdModel> _userAds = [];
  
  // الإعلان المحدد حالياً
  AdModel? _selectedAd;
  
  // فلاتر البحث
  String _searchQuery = '';
  String _selectedCategory = '';
  double _minPrice = 0;
  double _maxPrice = 10000;
  String _selectedLocation = '';
  
  // حالة التحميل
  bool _isLoading = false;
  
  // Getters
  List<AdModel> get ads => _ads;
  List<AdModel> get favoriteAds => _favoriteAds;
  List<AdModel> get userAds => _userAds;
  AdModel? get selectedAd => _selectedAd;
  String get searchQuery => _searchQuery;
  String get selectedCategory => _selectedCategory;
  double get minPrice => _minPrice;
  double get maxPrice => _maxPrice;
  String get selectedLocation => _selectedLocation;
  bool get isLoading => _isLoading;

  // الحصول على الإعلانات المفلترة
  List<AdModel> get filteredAds {
    List<AdModel> filtered = _ads;
    
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((ad) => 
        ad.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        ad.description.toLowerCase().contains(_searchQuery.toLowerCase())
      ).toList();
    }
    
    if (_selectedCategory.isNotEmpty) {
      filtered = filtered.where((ad) => ad.category == _selectedCategory).toList();
    }
    
    if (_selectedLocation.isNotEmpty) {
      filtered = filtered.where((ad) => ad.location == _selectedLocation).toList();
    }
    
    filtered = filtered.where((ad) => 
      ad.price >= _minPrice && ad.price <= _maxPrice
    ).toList();
    
    return filtered;
  }

  // الحصول على الإعلانات المميزة
  List<AdModel> get featuredAds {
    return _ads.where((ad) => ad.isFeatured).toList();
  }

  // تحديث قائمة الإعلانات
  void setAds(List<AdModel> ads) {
    _ads = ads;
    notifyListeners();
  }

  // إضافة إعلان جديد
  void addAd(AdModel ad) {
    _ads.insert(0, ad);
    _userAds.insert(0, ad);
    notifyListeners();
  }

  // حذف إعلان
  void removeAd(String adId) {
    _ads.removeWhere((ad) => ad.id == adId);
    _userAds.removeWhere((ad) => ad.id == adId);
    _favoriteAds.removeWhere((ad) => ad.id == adId);
    notifyListeners();
  }

  // تحديث إعلان
  void updateAd(AdModel updatedAd) {
    final index = _ads.indexWhere((ad) => ad.id == updatedAd.id);
    if (index != -1) {
      _ads[index] = updatedAd;
    }

    final userIndex = _userAds.indexWhere((ad) => ad.id == updatedAd.id);
    if (userIndex != -1) {
      _userAds[userIndex] = updatedAd;
    }

    // تحديث المفضلة أيضاً إذا كان الإعلان موجود فيها
    final favIndex = _favoriteAds.indexWhere((ad) => ad.id == updatedAd.id);
    if (favIndex != -1) {
      _favoriteAds[favIndex] = updatedAd;
    }

    notifyListeners();
  }



  // إضافة/إزالة من المفضلة
  void toggleFavorite(AdModel ad) {
    final index = _favoriteAds.indexWhere((fav) => fav.id == ad.id);
    if (index != -1) {
      _favoriteAds.removeAt(index);
    } else {
      _favoriteAds.add(ad);
    }
    notifyListeners();
  }

  // التحقق من وجود الإعلان في المفضلة
  bool isFavorite(String adId) {
    return _favoriteAds.any((ad) => ad.id == adId);
  }

  // تحديد الإعلان المحدد
  void setSelectedAd(AdModel ad) {
    _selectedAd = ad;
    // زيادة عدد المشاهدات
    final updatedAd = ad.copyWith(views: ad.views + 1);
    updateAd(updatedAd);
  }

  // تحديث فلاتر البحث
  void updateSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void updateSelectedCategory(String category) {
    _selectedCategory = category;
    notifyListeners();
  }

  void updatePriceRange(double min, double max) {
    _minPrice = min;
    _maxPrice = max;
    notifyListeners();
  }

  void updateSelectedLocation(String location) {
    _selectedLocation = location;
    notifyListeners();
  }

  // مسح الفلاتر
  void clearFilters() {
    _searchQuery = '';
    _selectedCategory = '';
    _minPrice = 0;
    _maxPrice = 10000;
    _selectedLocation = '';
    notifyListeners();
  }

  // تحديث حالة التحميل
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // تحميل البيانات التجريبية
  void loadSampleData() {
    _ads = [
      AdModel(
        id: '1',
        title: 'سيارة تويوتا كامري 2020',
        description: 'سيارة في حالة ممتازة، قليلة الاستعمال، صيانة دورية منتظمة',
        price: 8500,
        category: 'سيارات ومركبات',
        location: 'الكويت',
        images: [],
        sellerId: 'user1',
        sellerName: 'أحمد محمد',
        sellerPhone: '+965 9999 9999',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        isFeatured: true,
        views: 45,
      ),
      AdModel(
        id: '2',
        title: 'شقة للإيجار في السالمية',
        description: 'شقة 3 غرف وصالة، مفروشة بالكامل، إطلالة على البحر',
        price: 450,
        category: 'عقارات',
        location: 'السالمية',
        images: [],
        sellerId: 'user2',
        sellerName: 'فاطمة أحمد',
        sellerPhone: '+965 8888 8888',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        isFeatured: true,
        views: 32,
      ),
      AdModel(
        id: '3',
        title: 'آيفون 14 برو ماكس',
        description: 'جهاز جديد لم يستعمل، مع جميع الملحقات الأصلية',
        price: 350,
        category: 'إلكترونيات',
        location: 'حولي',
        images: [],
        sellerId: 'user3',
        sellerName: 'محمد علي',
        sellerPhone: '+965 7777 7777',
        createdAt: DateTime.now().subtract(const Duration(hours: 5)),
        isFeatured: false,
        views: 18,
      ),
      AdModel(
        id: '4',
        title: 'طقم صالة مودرن',
        description: 'طقم صالة 7 مقاعد، حالة ممتازة، لون بيج',
        price: 280,
        category: 'أثاث ومفروشات',
        location: 'الجهراء',
        images: [],
        sellerId: 'user4',
        sellerName: 'سارة خالد',
        sellerPhone: '+965 6666 6666',
        createdAt: DateTime.now().subtract(const Duration(hours: 12)),
        isFeatured: false,
        views: 12,
      ),
      AdModel(
        id: '5',
        title: 'فستان سهرة أنيق',
        description: 'فستان سهرة مقاس M، لبس مرة واحدة فقط',
        price: 45,
        category: 'ملابس وأزياء',
        location: 'الفروانية',
        images: [],
        sellerId: 'user5',
        sellerName: 'نورا سالم',
        sellerPhone: '+965 5555 5555',
        createdAt: DateTime.now().subtract(const Duration(hours: 8)),
        isFeatured: false,
        views: 8,
      ),
    ];
    
    notifyListeners();
  }
}
