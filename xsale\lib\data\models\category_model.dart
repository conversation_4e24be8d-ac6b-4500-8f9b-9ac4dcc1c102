import 'package:json_annotation/json_annotation.dart';

part 'category_model.g.dart';

@JsonSerializable()
class CategoryModel {
  final String id;
  final String nameAr;
  final String nameEn;
  final String? descriptionAr;
  final String? descriptionEn;
  final String icon;
  final String? image;
  final String color;
  final String? parentId;
  final List<CategoryModel>? subcategories;
  final List<CategoryField>? fields;
  final int order;
  final bool isActive;
  final int adCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CategoryModel({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    this.descriptionAr,
    this.descriptionEn,
    required this.icon,
    this.image,
    required this.color,
    this.parentId,
    this.subcategories,
    this.fields,
    required this.order,
    required this.isActive,
    required this.adCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryModelToJson(this);

  String getName(String locale) {
    return locale == 'ar' ? nameAr : nameEn;
  }

  String? getDescription(String locale) {
    return locale == 'ar' ? descriptionAr : descriptionEn;
  }

  bool get hasSubcategories => subcategories != null && subcategories!.isNotEmpty;

  bool get isMainCategory => parentId == null;

  CategoryModel copyWith({
    String? id,
    String? nameAr,
    String? nameEn,
    String? descriptionAr,
    String? descriptionEn,
    String? icon,
    String? image,
    String? color,
    String? parentId,
    List<CategoryModel>? subcategories,
    List<CategoryField>? fields,
    int? order,
    bool? isActive,
    int? adCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      descriptionAr: descriptionAr ?? this.descriptionAr,
      descriptionEn: descriptionEn ?? this.descriptionEn,
      icon: icon ?? this.icon,
      image: image ?? this.image,
      color: color ?? this.color,
      parentId: parentId ?? this.parentId,
      subcategories: subcategories ?? this.subcategories,
      fields: fields ?? this.fields,
      order: order ?? this.order,
      isActive: isActive ?? this.isActive,
      adCount: adCount ?? this.adCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class CategoryField {
  final String id;
  final String nameAr;
  final String nameEn;
  final String type; // text, number, select, multiselect, boolean, date
  final bool isRequired;
  final List<CategoryFieldOption>? options;
  final String? placeholder;
  final String? validation;
  final int order;

  const CategoryField({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.type,
    required this.isRequired,
    this.options,
    this.placeholder,
    this.validation,
    required this.order,
  });

  factory CategoryField.fromJson(Map<String, dynamic> json) =>
      _$CategoryFieldFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryFieldToJson(this);

  String getName(String locale) {
    return locale == 'ar' ? nameAr : nameEn;
  }

  bool get hasOptions => options != null && options!.isNotEmpty;
}

@JsonSerializable()
class CategoryFieldOption {
  final String id;
  final String nameAr;
  final String nameEn;
  final String? value;
  final int order;

  const CategoryFieldOption({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    this.value,
    required this.order,
  });

  factory CategoryFieldOption.fromJson(Map<String, dynamic> json) =>
      _$CategoryFieldOptionFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryFieldOptionToJson(this);

  String getName(String locale) {
    return locale == 'ar' ? nameAr : nameEn;
  }
}

// Predefined main categories
class PredefinedCategories {
  static const List<Map<String, dynamic>> mainCategories = [
    {
      'id': 'vehicles',
      'nameAr': 'السيارات والمركبات',
      'nameEn': 'Vehicles',
      'icon': 'directions_car',
      'color': '#3B82F6',
      'order': 1,
    },
    {
      'id': 'real_estate',
      'nameAr': 'العقارات',
      'nameEn': 'Real Estate',
      'icon': 'home',
      'color': '#10B981',
      'order': 2,
    },
    {
      'id': 'electronics',
      'nameAr': 'الإلكترونيات',
      'nameEn': 'Electronics',
      'icon': 'devices',
      'color': '#8B5CF6',
      'order': 3,
    },
    {
      'id': 'fashion',
      'nameAr': 'الأزياء والموضة',
      'nameEn': 'Fashion',
      'icon': 'checkroom',
      'color': '#EC4899',
      'order': 4,
    },
    {
      'id': 'home_garden',
      'nameAr': 'المنزل والحديقة',
      'nameEn': 'Home & Garden',
      'icon': 'home_work',
      'color': '#06B6D4',
      'order': 5,
    },
    {
      'id': 'jobs',
      'nameAr': 'الوظائف',
      'nameEn': 'Jobs',
      'icon': 'work',
      'color': '#F59E0B',
      'order': 6,
    },
    {
      'id': 'services',
      'nameAr': 'الخدمات',
      'nameEn': 'Services',
      'icon': 'build',
      'color': '#84CC16',
      'order': 7,
    },
    {
      'id': 'hobbies_sports',
      'nameAr': 'الهوايات والرياضة',
      'nameEn': 'Hobbies & Sports',
      'icon': 'sports_soccer',
      'color': '#EF4444',
      'order': 8,
    },
  ];
}
