class AdModel {
  final String id;
  final String title;
  final String description;
  final double price;
  final String category;
  final String location;
  final List<String> images;
  final String sellerId;
  final String sellerName;
  final String sellerPhone;
  final DateTime createdAt;
  final bool isFeatured;
  final bool isActive;
  final int views;

  AdModel({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.category,
    required this.location,
    required this.images,
    required this.sellerId,
    required this.sellerName,
    required this.sellerPhone,
    required this.createdAt,
    this.isFeatured = false,
    this.isActive = true,
    this.views = 0,
  });

  factory AdModel.fromJson(Map<String, dynamic> json) {
    return AdModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      category: json['category'] ?? '',
      location: json['location'] ?? '',
      images: List<String>.from(json['images'] ?? []),
      sellerId: json['sellerId'] ?? '',
      sellerName: json['sellerName'] ?? '',
      sellerPhone: json['sellerPhone'] ?? '',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      isFeatured: json['isFeatured'] ?? false,
      isActive: json['isActive'] ?? true,
      views: json['views'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'price': price,
      'category': category,
      'location': location,
      'images': images,
      'sellerId': sellerId,
      'sellerName': sellerName,
      'sellerPhone': sellerPhone,
      'createdAt': createdAt.toIso8601String(),
      'isFeatured': isFeatured,
      'isActive': isActive,
      'views': views,
    };
  }

  AdModel copyWith({
    String? id,
    String? title,
    String? description,
    double? price,
    String? category,
    String? location,
    List<String>? images,
    String? sellerId,
    String? sellerName,
    String? sellerPhone,
    DateTime? createdAt,
    bool? isFeatured,
    bool? isActive,
    int? views,
  }) {
    return AdModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      category: category ?? this.category,
      location: location ?? this.location,
      images: images ?? this.images,
      sellerId: sellerId ?? this.sellerId,
      sellerName: sellerName ?? this.sellerName,
      sellerPhone: sellerPhone ?? this.sellerPhone,
      createdAt: createdAt ?? this.createdAt,
      isFeatured: isFeatured ?? this.isFeatured,
      isActive: isActive ?? this.isActive,
      views: views ?? this.views,
    );
  }
}

class CategoryModel {
  final String id;
  final String name;
  final String icon;
  final String color;
  final int adCount;

  CategoryModel({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
    this.adCount = 0,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      icon: json['icon'] ?? '',
      color: json['color'] ?? '',
      adCount: json['adCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'color': color,
      'adCount': adCount,
    };
  }
}
