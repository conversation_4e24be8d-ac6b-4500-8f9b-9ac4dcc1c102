import 'package:json_annotation/json_annotation.dart';
import 'user_model.dart';
import 'category_model.dart';

part 'ad_model.g.dart';

@JsonSerializable()
class AdModel {
  final String id;
  final String title;
  final String description;
  final double price;
  final String currency;
  final bool isPriceNegotiable;
  final String categoryId;
  final CategoryModel? category;
  final String userId;
  final UserModel? user;
  final List<String> images;
  final List<String>? videos;
  final AdLocation? location;
  final Map<String, dynamic>? customFields;
  final String status;
  final bool isFeatured;
  final bool isUrgent;
  final int viewCount;
  final int favoriteCount;
  final int contactCount;
  final List<String> tags;
  final DateTime? expiresAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AdModel({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.currency,
    required this.isPriceNegotiable,
    required this.categoryId,
    this.category,
    required this.userId,
    this.user,
    required this.images,
    this.videos,
    this.location,
    this.customFields,
    required this.status,
    required this.isFeatured,
    required this.isUrgent,
    required this.viewCount,
    required this.favoriteCount,
    required this.contactCount,
    required this.tags,
    this.expiresAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AdModel.fromJson(Map<String, dynamic> json) =>
      _$AdModelFromJson(json);

  Map<String, dynamic> toJson() => _$AdModelToJson(this);

  bool get hasImages => images.isNotEmpty;
  bool get hasVideos => videos != null && videos!.isNotEmpty;
  bool get hasLocation => location != null;
  bool get isActive => status == 'active';
  bool get isPending => status == 'pending';
  bool get isSold => status == 'sold';
  bool get isExpired => status == 'expired' || 
      (expiresAt != null && expiresAt!.isBefore(DateTime.now()));

  String get mainImage => images.isNotEmpty ? images.first : '';

  String get formattedPrice {
    if (price == 0) return 'مجاناً';
    return '${price.toStringAsFixed(price.truncateToDouble() == price ? 0 : 2)} $currency';
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  AdModel copyWith({
    String? id,
    String? title,
    String? description,
    double? price,
    String? currency,
    bool? isPriceNegotiable,
    String? categoryId,
    CategoryModel? category,
    String? userId,
    UserModel? user,
    List<String>? images,
    List<String>? videos,
    AdLocation? location,
    Map<String, dynamic>? customFields,
    String? status,
    bool? isFeatured,
    bool? isUrgent,
    int? viewCount,
    int? favoriteCount,
    int? contactCount,
    List<String>? tags,
    DateTime? expiresAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AdModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      isPriceNegotiable: isPriceNegotiable ?? this.isPriceNegotiable,
      categoryId: categoryId ?? this.categoryId,
      category: category ?? this.category,
      userId: userId ?? this.userId,
      user: user ?? this.user,
      images: images ?? this.images,
      videos: videos ?? this.videos,
      location: location ?? this.location,
      customFields: customFields ?? this.customFields,
      status: status ?? this.status,
      isFeatured: isFeatured ?? this.isFeatured,
      isUrgent: isUrgent ?? this.isUrgent,
      viewCount: viewCount ?? this.viewCount,
      favoriteCount: favoriteCount ?? this.favoriteCount,
      contactCount: contactCount ?? this.contactCount,
      tags: tags ?? this.tags,
      expiresAt: expiresAt ?? this.expiresAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class AdLocation {
  final String? address;
  final String? city;
  final String? state;
  final String? country;
  final double latitude;
  final double longitude;
  final double? accuracy;

  const AdLocation({
    this.address,
    this.city,
    this.state,
    this.country,
    required this.latitude,
    required this.longitude,
    this.accuracy,
  });

  factory AdLocation.fromJson(Map<String, dynamic> json) =>
      _$AdLocationFromJson(json);

  Map<String, dynamic> toJson() => _$AdLocationToJson(this);

  String get displayAddress {
    if (address != null && address!.isNotEmpty) {
      return address!;
    }
    
    final parts = <String>[];
    if (city != null && city!.isNotEmpty) parts.add(city!);
    if (state != null && state!.isNotEmpty) parts.add(state!);
    if (country != null && country!.isNotEmpty) parts.add(country!);
    
    return parts.join(', ');
  }
}

// Ad status constants
class AdStatus {
  static const String pending = 'pending';
  static const String active = 'active';
  static const String sold = 'sold';
  static const String expired = 'expired';
  static const String rejected = 'rejected';
  static const String draft = 'draft';
  
  static const List<String> all = [
    pending,
    active,
    sold,
    expired,
    rejected,
    draft,
  ];
  
  static String getDisplayName(String status) {
    switch (status) {
      case pending:
        return 'قيد المراجعة';
      case active:
        return 'نشط';
      case sold:
        return 'تم البيع';
      case expired:
        return 'منتهي الصلاحية';
      case rejected:
        return 'مرفوض';
      case draft:
        return 'مسودة';
      default:
        return 'غير معروف';
    }
  }
}
