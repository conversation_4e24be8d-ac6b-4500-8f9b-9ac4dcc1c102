import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import 'api_service.dart';
import '../../core/constants/app_constants.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final ApiService _apiService = ApiService();
  UserModel? _currentUser;

  UserModel? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;

  Future<void> initialize() async {
    await _loadUserFromStorage();
  }

  Future<AuthResult> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        '/auth/login',
        data: {
          'email': email,
          'password': password,
        },
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final token = data['token'] as String;
        final userData = data['user'] as Map<String, dynamic>;
        
        final user = UserModel.fromJson(userData);
        
        await _saveUserSession(user, token);
        
        return AuthResult.success(user);
      } else {
        return AuthResult.error(response.error ?? 'فشل في تسجيل الدخول');
      }
    } catch (e) {
      return AuthResult.error('خطأ في الاتصال: ${e.toString()}');
    }
  }

  Future<AuthResult> register({
    required String name,
    required String email,
    required String password,
    String? phone,
  }) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        '/auth/register',
        data: {
          'name': name,
          'email': email,
          'password': password,
          if (phone != null) 'phone': phone,
        },
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final token = data['token'] as String;
        final userData = data['user'] as Map<String, dynamic>;
        
        final user = UserModel.fromJson(userData);
        
        await _saveUserSession(user, token);
        
        return AuthResult.success(user);
      } else {
        return AuthResult.error(response.error ?? 'فشل في إنشاء الحساب');
      }
    } catch (e) {
      return AuthResult.error('خطأ في الاتصال: ${e.toString()}');
    }
  }

  Future<AuthResult> loginWithPhone({
    required String phone,
    required String otp,
  }) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        '/auth/login-phone',
        data: {
          'phone': phone,
          'otp': otp,
        },
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final token = data['token'] as String;
        final userData = data['user'] as Map<String, dynamic>;
        
        final user = UserModel.fromJson(userData);
        
        await _saveUserSession(user, token);
        
        return AuthResult.success(user);
      } else {
        return AuthResult.error(response.error ?? 'فشل في تسجيل الدخول');
      }
    } catch (e) {
      return AuthResult.error('خطأ في الاتصال: ${e.toString()}');
    }
  }

  Future<bool> sendOTP(String phone) async {
    try {
      final response = await _apiService.post(
        '/auth/send-otp',
        data: {'phone': phone},
      );
      return response.isSuccess;
    } catch (e) {
      return false;
    }
  }

  Future<AuthResult> resetPassword({
    required String email,
  }) async {
    try {
      final response = await _apiService.post(
        '/auth/reset-password',
        data: {'email': email},
      );

      if (response.isSuccess) {
        return AuthResult.success(null, message: 'تم إرسال رابط إعادة تعيين كلمة المرور');
      } else {
        return AuthResult.error(response.error ?? 'فشل في إرسال رابط إعادة التعيين');
      }
    } catch (e) {
      return AuthResult.error('خطأ في الاتصال: ${e.toString()}');
    }
  }

  Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await _apiService.put(
        '/auth/change-password',
        data: {
          'currentPassword': currentPassword,
          'newPassword': newPassword,
        },
      );

      if (response.isSuccess) {
        return AuthResult.success(null, message: 'تم تغيير كلمة المرور بنجاح');
      } else {
        return AuthResult.error(response.error ?? 'فشل في تغيير كلمة المرور');
      }
    } catch (e) {
      return AuthResult.error('خطأ في الاتصال: ${e.toString()}');
    }
  }

  Future<AuthResult> updateProfile({
    String? name,
    String? phone,
    String? bio,
    String? location,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (name != null) data['name'] = name;
      if (phone != null) data['phone'] = phone;
      if (bio != null) data['bio'] = bio;
      if (location != null) data['location'] = location;

      final response = await _apiService.put<Map<String, dynamic>>(
        '/auth/profile',
        data: data,
        fromJson: (json) => json,
      );

      if (response.isSuccess && response.data != null) {
        final userData = response.data!['user'] as Map<String, dynamic>;
        final user = UserModel.fromJson(userData);
        
        await _updateUserInStorage(user);
        
        return AuthResult.success(user, message: 'تم تحديث الملف الشخصي');
      } else {
        return AuthResult.error(response.error ?? 'فشل في تحديث الملف الشخصي');
      }
    } catch (e) {
      return AuthResult.error('خطأ في الاتصال: ${e.toString()}');
    }
  }

  Future<void> logout() async {
    try {
      await _apiService.post('/auth/logout');
    } catch (e) {
      // Ignore logout API errors
    } finally {
      await _clearUserSession();
    }
  }

  Future<bool> refreshToken() async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        '/auth/refresh',
        fromJson: (json) => json,
      );

      if (response.isSuccess && response.data != null) {
        final token = response.data!['token'] as String;
        await _apiService.setAuthToken(token);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<void> _saveUserSession(UserModel user, String token) async {
    _currentUser = user;
    await _apiService.setAuthToken(token);
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.userDataKey, jsonEncode(user.toJson()));
  }

  Future<void> _updateUserInStorage(UserModel user) async {
    _currentUser = user;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.userDataKey, jsonEncode(user.toJson()));
  }

  Future<void> _loadUserFromStorage() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString(AppConstants.userDataKey);
    
    if (userDataString != null) {
      try {
        final userData = jsonDecode(userDataString) as Map<String, dynamic>;
        _currentUser = UserModel.fromJson(userData);
        await _apiService.loadAuthToken();
      } catch (e) {
        // Clear invalid user data
        await _clearUserSession();
      }
    }
  }

  Future<void> _clearUserSession() async {
    _currentUser = null;
    await _apiService.clearAuthToken();
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.userDataKey);
  }
}

class AuthResult {
  final UserModel? user;
  final String? error;
  final String? message;
  final bool isSuccess;

  AuthResult.success(this.user, {this.message})
      : error = null,
        isSuccess = true;

  AuthResult.error(this.error)
      : user = null,
        message = null,
        isSuccess = false;
}
