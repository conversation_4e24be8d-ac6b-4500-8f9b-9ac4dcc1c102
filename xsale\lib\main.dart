


import 'package:flutter/material.dart';
// تمت إزالة ميزة المشاركة مؤقتاً لدعم الويب بدون مشاكل.

void main() {
  runApp(const XSaleApp());
}

class XSaleApp extends StatelessWidget {
  const XSaleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'XSale - بيع وشراء',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const MainScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;

  final List<Widget> _pages = <Widget>[
    ProductsPage(),
    AddProductPage(),
    CartPage(),
    AccountPage(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('XSale - سوق البيع والشراء'),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        items: <BottomNavigationBarItem>[
          const BottomNavigationBarItem(
            icon: Icon(Icons.store),
            label: 'المنتجات',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.add_box),
            label: 'إضافة منتج',
          ),
          BottomNavigationBarItem(
            icon: Stack(
              children: [
                const Icon(Icons.shopping_cart),
                if (cartProducts.isNotEmpty)
                  Positioned(
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        '${cartProducts.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            label: 'السلة',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'الحساب',
          ),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        onTap: _onItemTapped,
      ),
    );
  }
}

// نموذج بيانات المنتج
class Product {
  final String name;
  final String description;
  final double price;
  final String imageUrl;

  Product({required this.name, required this.description, required this.price, required this.imageUrl});
}

// قائمة منتجات وهمية
List<Product> demoProducts = [
  Product(
    name: 'هاتف ذكي',
    description: 'هاتف ذكي جديد مع كاميرا عالية الدقة.',
    price: 1200.0,
    imageUrl: 'https://cdn-icons-png.flaticon.com/512/747/747376.png',
  ),
  Product(
    name: 'حاسوب محمول',
    description: 'حاسوب محمول للأعمال والدراسة.',
    price: 3500.0,
    imageUrl: 'https://cdn-icons-png.flaticon.com/512/747/747314.png',
  ),
  Product(
    name: 'سماعات لاسلكية',
    description: 'سماعات بجودة صوت عالية.',
    price: 300.0,
    imageUrl: 'https://cdn-icons-png.flaticon.com/512/747/747310.png',
  ),
];

// قائمة السلة (عالمية)
List<Product> cartProducts = [];
// قائمة المفضلة (عالمية)
List<Product> favoriteProducts = [];

// صفحة المنتجات
class ProductsPage extends StatefulWidget {
  @override
  State<ProductsPage> createState() => _ProductsPageState();
}

class _ProductsPageState extends State<ProductsPage> {
  String search = '';
  String filter = 'all'; // all, fav
  String sort = 'none'; // none, low, high

  @override
  Widget build(BuildContext context) {
    List<Product> filtered = demoProducts.where((p) =>
      (filter == 'all' || (filter == 'fav' && favoriteProducts.contains(p))) &&
      (p.name.contains(search) || p.description.contains(search))
    ).toList();
    if (sort == 'low') {
      filtered.sort((a, b) => a.price.compareTo(b.price));
    } else if (sort == 'high') {
      filtered.sort((a, b) => b.price.compareTo(a.price));
    }
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'ابحث عن منتج...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                  onChanged: (val) => setState(() => search = val),
                ),
              ),
              const SizedBox(width: 8),
              PopupMenuButton<String>(
                icon: Icon(Icons.filter_alt),
                tooltip: 'فلترة',
                onSelected: (val) => setState(() => filter = val),
                itemBuilder: (context) => [
                  CheckedPopupMenuItem(
                    value: 'all',
                    checked: filter == 'all',
                    child: Text('الكل'),
                  ),
                  CheckedPopupMenuItem(
                    value: 'fav',
                    checked: filter == 'fav',
                    child: Text('المفضلة فقط'),
                  ),
                ],
              ),
              PopupMenuButton<String>(
                icon: Icon(Icons.sort),
                tooltip: 'ترتيب',
                onSelected: (val) => setState(() => sort = val),
                itemBuilder: (context) => [
                  CheckedPopupMenuItem(
                    value: 'none',
                    checked: sort == 'none',
                    child: Text('بدون ترتيب'),
                  ),
                  CheckedPopupMenuItem(
                    value: 'low',
                    checked: sort == 'low',
                    child: Text('الأقل سعراً'),
                  ),
                  CheckedPopupMenuItem(
                    value: 'high',
                    checked: sort == 'high',
                    child: Text('الأعلى سعراً'),
                  ),
                ],
              ),
            ],
          ),
        ),
        Expanded(
          child: filtered.isEmpty
              ? Center(child: Text('لا توجد منتجات مطابقة'))
              : ListView.builder(
                  padding: const EdgeInsets.all(12),
                  itemCount: filtered.length,
                  itemBuilder: (context, index) {
                    final product = filtered[index];
                    final isFav = favoriteProducts.contains(product);
                    return Card(
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      child: ListTile(
                        leading: Image.network(product.imageUrl, width: 50, height: 50, fit: BoxFit.cover),
                        title: Text(product.name, style: TextStyle(fontWeight: FontWeight.bold)),
                        subtitle: Text(product.description),
                        trailing: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text('${product.price.toStringAsFixed(0)} ر.س', style: TextStyle(color: Colors.deepPurple)),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: Icon(Icons.add_shopping_cart),
                                  onPressed: () {
                                    if (cartProducts.contains(product)) {
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(content: Text('المنتج موجود بالفعل في السلة!')),
                                      );
                                    } else {
                                      cartProducts.add(product);
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(content: Text('تمت إضافة المنتج إلى السلة!')),
                                      );
                                    }
                                    setState(() {});
                                  },
                                ),
                                IconButton(
                                  icon: Icon(isFav ? Icons.favorite : Icons.favorite_border, color: isFav ? Colors.red : null),
                                  onPressed: () {
                                    setState(() {
                                      if (isFav) {
                                        favoriteProducts.remove(product);
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(content: Text('تمت إزالة المنتج من المفضلة')),
                                        );
                                      } else {
                                        favoriteProducts.add(product);
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(content: Text('تمت إضافة المنتج إلى المفضلة!')),
                                        );
                                      }
                                    });
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (_) => ProductDetailsDialog(product: product),
                          );
                        },
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }
}

class ProductDetailsDialog extends StatelessWidget {
  final Product product;
  const ProductDetailsDialog({required this.product});

  @override
  Widget build(BuildContext context) {
    final isFav = favoriteProducts.contains(product);
    return AlertDialog(
      title: Row(
        children: [
          Expanded(child: Text(product.name)),
          IconButton(
            icon: Icon(isFav ? Icons.favorite : Icons.favorite_border, color: isFav ? Colors.red : null),
            onPressed: () {
              if (isFav) {
                favoriteProducts.remove(product);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('تمت إزالة المنتج من المفضلة')),
                );
              } else {
                favoriteProducts.add(product);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('تمت إضافة المنتج إلى المفضلة!')),
                );
              }
              Navigator.pop(context);
            },
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.network(product.imageUrl, width: 120, height: 120),
          SizedBox(height: 10),
          Text(product.description),
          SizedBox(height: 10),
          Text('السعر: ${product.price.toStringAsFixed(0)} ر.س', style: TextStyle(color: Colors.deepPurple)),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('إغلاق'),
        ),
        ElevatedButton(
          onPressed: () {
            if (cartProducts.contains(product)) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('المنتج موجود بالفعل في السلة!')),
              );
            } else {
              cartProducts.add(product);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('تمت إضافة المنتج إلى السلة!')),
              );
            }
            Navigator.pop(context);
          },
          child: Text('إضافة للسلة'),
        ),
      ],
    );
  }
}

// صفحة إضافة منتج
class AddProductPage extends StatefulWidget {
  @override
  State<AddProductPage> createState() => _AddProductPageState();
}

class _AddProductPageState extends State<AddProductPage> {
  final _formKey = GlobalKey<FormState>();
  String name = '';
  String description = '';
  String imageUrl = '';
  double price = 0;
  String previewUrl = '';

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: ListView(
          children: [
            TextFormField(
              decoration: InputDecoration(labelText: 'اسم المنتج'),
              validator: (value) => value == null || value.isEmpty ? 'أدخل اسم المنتج' : null,
              onSaved: (value) => name = value ?? '',
            ),
            TextFormField(
              decoration: InputDecoration(labelText: 'وصف المنتج'),
              validator: (value) => value == null || value.isEmpty ? 'أدخل وصف المنتج' : null,
              onSaved: (value) => description = value ?? '',
            ),
            TextFormField(
              decoration: InputDecoration(labelText: 'رابط صورة المنتج'),
              validator: (value) => value == null || value.isEmpty ? 'أدخل رابط الصورة' : null,
              onChanged: (val) => setState(() => previewUrl = val),
              onSaved: (value) => imageUrl = value ?? '',
            ),
            if (previewUrl.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Image.network(previewUrl, height: 80, errorBuilder: (c, e, s) => Text('رابط غير صالح')),
              ),
            TextFormField(
              decoration: InputDecoration(labelText: 'السعر'),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) return 'أدخل السعر';
                if (double.tryParse(value) == null) return 'أدخل رقمًا صحيحًا';
                return null;
              },
              onSaved: (value) => price = double.tryParse(value ?? '0') ?? 0,
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  _formKey.currentState!.save();
                  demoProducts.add(Product(
                    name: name,
                    description: description,
                    price: price,
                    imageUrl: imageUrl,
                  ));
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('تمت إضافة المنتج!')),
                  );
                  _formKey.currentState!.reset();
                  setState(() { previewUrl = ''; });
                }
              },
              child: Text('إضافة'),
            ),
          ],
        ),
      ),
    );
  }
}

// صفحة السلة
class CartPage extends StatefulWidget {
  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  @override
  Widget build(BuildContext context) {
    if (cartProducts.isEmpty) {
      return Center(child: Text('السلة فارغة', style: TextStyle(fontSize: 22)));
    }
    double total = cartProducts.fold(0, (sum, item) => sum + item.price);
    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            itemCount: cartProducts.length,
            itemBuilder: (context, index) {
              final product = cartProducts[index];
              return ListTile(
                leading: Image.network(product.imageUrl, width: 40, height: 40),
                title: Text(product.name),
                subtitle: Text('${product.price.toStringAsFixed(0)} ر.س'),
                trailing: IconButton(
                  icon: Icon(Icons.delete, color: Colors.red),
                  onPressed: () {
                    setState(() {
                      cartProducts.removeAt(index);
                    });
                  },
                ),
              );
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('الإجمالي:', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text('${total.toStringAsFixed(0)} ر.س', style: TextStyle(fontSize: 18, color: Colors.deepPurple)),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: ElevatedButton(
            onPressed: () {
              setState(() {
                cartProducts.clear();
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('تمت عملية الشراء بنجاح!')),
              );
            },
            child: Text('شراء'),
          ),
        ),
      ],
    );
  }
}

// صفحة الحساب
class AccountPage extends StatefulWidget {
  @override
  State<AccountPage> createState() => _AccountPageState();
}

class _AccountPageState extends State<AccountPage> {
  String username = 'تاجر المستقبل';
  String email = '<EMAIL>';
  final _formKey = GlobalKey<FormState>();
  bool edit = false;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CircleAvatar(
            radius: 40,
            backgroundImage: NetworkImage('https://cdn-icons-png.flaticon.com/512/3135/3135715.png'),
          ),
          SizedBox(height: 16),
          if (!edit) ...[
            Text('اسم المستخدم: $username', style: TextStyle(fontSize: 20)),
            SizedBox(height: 8),
            Text('البريد: $email', style: TextStyle(fontSize: 16)),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => setState(() => edit = true),
              child: Text('تعديل المعلومات'),
            ),
            ElevatedButton(
              onPressed: () {},
              child: Text('تسجيل الخروج'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            ),
          ] else ...[
            Form(
              key: _formKey,
              child: Column(
                children: [
                  TextFormField(
                    initialValue: username,
                    decoration: InputDecoration(labelText: 'اسم المستخدم'),
                    validator: (v) => v == null || v.isEmpty ? 'أدخل الاسم' : null,
                    onSaved: (v) => username = v ?? username,
                  ),
                  TextFormField(
                    initialValue: email,
                    decoration: InputDecoration(labelText: 'البريد'),
                    validator: (v) => v == null || v.isEmpty ? 'أدخل البريد' : null,
                    onSaved: (v) => email = v ?? email,
                  ),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        _formKey.currentState!.save();
                        setState(() => edit = false);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('تم تحديث المعلومات!')),
                        );
                      }
                    },
                    child: Text('حفظ'),
                  ),
                  TextButton(
                    onPressed: () => setState(() => edit = false),
                    child: Text('إلغاء'),
                  ),
                ],
              ),
            ),
          ]
        ],
      ),
    );
  }
}
