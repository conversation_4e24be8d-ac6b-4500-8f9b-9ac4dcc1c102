import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  final String id;
  final String email;
  final String? phone;
  final String name;
  final String? avatar;
  final String role;
  final bool isVerified;
  final bool isActive;
  final UserProfile? profile;
  final UserSettings? settings;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserModel({
    required this.id,
    required this.email,
    this.phone,
    required this.name,
    this.avatar,
    required this.role,
    required this.isVerified,
    required this.isActive,
    this.profile,
    this.settings,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  UserModel copyWith({
    String? id,
    String? email,
    String? phone,
    String? name,
    String? avatar,
    String? role,
    bool? isVerified,
    bool? isActive,
    UserProfile? profile,
    UserSettings? settings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      role: role ?? this.role,
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
      profile: profile ?? this.profile,
      settings: settings ?? this.settings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class UserProfile {
  final String? bio;
  final String? location;
  final String? city;
  final String? country;
  final double? latitude;
  final double? longitude;
  final String? website;
  final List<String>? socialLinks;
  final UserRating? rating;
  final int totalAds;
  final int activeAds;
  final int soldAds;

  const UserProfile({
    this.bio,
    this.location,
    this.city,
    this.country,
    this.latitude,
    this.longitude,
    this.website,
    this.socialLinks,
    this.rating,
    required this.totalAds,
    required this.activeAds,
    required this.soldAds,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);

  Map<String, dynamic> toJson() => _$UserProfileToJson(this);
}

@JsonSerializable()
class UserRating {
  final double average;
  final int totalReviews;
  final Map<int, int> distribution; // star -> count

  const UserRating({
    required this.average,
    required this.totalReviews,
    required this.distribution,
  });

  factory UserRating.fromJson(Map<String, dynamic> json) =>
      _$UserRatingFromJson(json);

  Map<String, dynamic> toJson() => _$UserRatingToJson(this);
}

@JsonSerializable()
class UserSettings {
  final String language;
  final String currency;
  final bool notificationsEnabled;
  final bool emailNotifications;
  final bool smsNotifications;
  final bool pushNotifications;
  final bool showPhone;
  final bool showEmail;
  final bool showLocation;
  final String theme;

  const UserSettings({
    required this.language,
    required this.currency,
    required this.notificationsEnabled,
    required this.emailNotifications,
    required this.smsNotifications,
    required this.pushNotifications,
    required this.showPhone,
    required this.showEmail,
    required this.showLocation,
    required this.theme,
  });

  factory UserSettings.fromJson(Map<String, dynamic> json) =>
      _$UserSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$UserSettingsToJson(this);

  UserSettings copyWith({
    String? language,
    String? currency,
    bool? notificationsEnabled,
    bool? emailNotifications,
    bool? smsNotifications,
    bool? pushNotifications,
    bool? showPhone,
    bool? showEmail,
    bool? showLocation,
    String? theme,
  }) {
    return UserSettings(
      language: language ?? this.language,
      currency: currency ?? this.currency,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      pushNotifications: pushNotifications ?? this.pushNotifications,
      showPhone: showPhone ?? this.showPhone,
      showEmail: showEmail ?? this.showEmail,
      showLocation: showLocation ?? this.showLocation,
      theme: theme ?? this.theme,
    );
  }

  static UserSettings get defaultSettings => const UserSettings(
        language: 'ar',
        currency: 'KWD',
        notificationsEnabled: true,
        emailNotifications: true,
        smsNotifications: false,
        pushNotifications: true,
        showPhone: true,
        showEmail: false,
        showLocation: true,
        theme: 'light',
      );
}
