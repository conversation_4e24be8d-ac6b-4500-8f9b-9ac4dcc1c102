// import 'package:json_annotation/json_annotation.dart';

// part 'user_model.g.dart';

// @JsonSerializable()
class UserModel {
  final String id;
  final String email;
  final String? phone;
  final String name;
  final String? avatar;
  final String role;
  final bool isVerified;
  final bool isActive;
  final UserProfile? profile;
  final UserSettings? settings;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserModel({
    required this.id,
    required this.email,
    this.phone,
    required this.name,
    this.avatar,
    required this.role,
    required this.isVerified,
    required this.isActive,
    this.profile,
    this.settings,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      isEmailVerified: json['isEmailVerified'] as bool? ?? false,
      phoneNumber: json['phoneNumber'] as String?,
      isPhoneVerified: json['isPhoneVerified'] as bool? ?? false,
      profile: UserProfile.fromJson(json['profile'] as Map<String, dynamic>),
      rating: json['rating'] != null
          ? UserRating.fromJson(json['rating'] as Map<String, dynamic>)
          : null,
      settings: UserSettings.fromJson(json['settings'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      lastLoginAt: json['lastLoginAt'] != null
          ? DateTime.parse(json['lastLoginAt'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'isEmailVerified': isEmailVerified,
      'phoneNumber': phoneNumber,
      'isPhoneVerified': isPhoneVerified,
      'profile': profile.toJson(),
      'rating': rating?.toJson(),
      'settings': settings.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? phone,
    String? name,
    String? avatar,
    String? role,
    bool? isVerified,
    bool? isActive,
    UserProfile? profile,
    UserSettings? settings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      role: role ?? this.role,
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
      profile: profile ?? this.profile,
      settings: settings ?? this.settings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

// @JsonSerializable()
class UserProfile {
  final String? bio;
  final String? location;
  final String? city;
  final String? country;
  final double? latitude;
  final double? longitude;
  final String? website;
  final List<String>? socialLinks;
  final UserRating? rating;
  final int totalAds;
  final int activeAds;
  final int soldAds;

  const UserProfile({
    this.bio,
    this.location,
    this.city,
    this.country,
    this.latitude,
    this.longitude,
    this.website,
    this.socialLinks,
    this.rating,
    required this.totalAds,
    required this.activeAds,
    required this.soldAds,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      profilePicture: json['profilePicture'] as String?,
      bio: json['bio'] as String?,
      location: json['location'] as String?,
      dateOfBirth: json['dateOfBirth'] != null
          ? DateTime.parse(json['dateOfBirth'] as String)
          : null,
      gender: json['gender'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'profilePicture': profilePicture,
      'bio': bio,
      'location': location,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'gender': gender,
    };
  }
}

// @JsonSerializable()
class UserRating {
  final double average;
  final int totalReviews;
  final Map<int, int> distribution; // star -> count

  const UserRating({
    required this.average,
    required this.totalReviews,
    required this.distribution,
  });

  factory UserRating.fromJson(Map<String, dynamic> json) {
    return UserRating(
      averageRating: (json['averageRating'] as num).toDouble(),
      totalRatings: json['totalRatings'] as int,
      fiveStars: json['fiveStars'] as int,
      fourStars: json['fourStars'] as int,
      threeStars: json['threeStars'] as int,
      twoStars: json['twoStars'] as int,
      oneStar: json['oneStar'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'averageRating': averageRating,
      'totalRatings': totalRatings,
      'fiveStars': fiveStars,
      'fourStars': fourStars,
      'threeStars': threeStars,
      'twoStars': twoStars,
      'oneStar': oneStar,
    };
  }
}

// @JsonSerializable()
class UserSettings {
  final String language;
  final String currency;
  final bool notificationsEnabled;
  final bool emailNotifications;
  final bool smsNotifications;
  final bool pushNotifications;
  final bool showPhone;
  final bool showEmail;
  final bool showLocation;
  final String theme;

  const UserSettings({
    required this.language,
    required this.currency,
    required this.notificationsEnabled,
    required this.emailNotifications,
    required this.smsNotifications,
    required this.pushNotifications,
    required this.showPhone,
    required this.showEmail,
    required this.showLocation,
    required this.theme,
  });

  factory UserSettings.fromJson(Map<String, dynamic> json) {
    return UserSettings(
      language: json['language'] as String? ?? 'ar',
      theme: json['theme'] as String? ?? 'system',
      notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
      emailNotifications: json['emailNotifications'] as bool? ?? true,
      smsNotifications: json['smsNotifications'] as bool? ?? false,
      pushNotifications: json['pushNotifications'] as bool? ?? true,
      locationEnabled: json['locationEnabled'] as bool? ?? true,
      showOnlineStatus: json['showOnlineStatus'] as bool? ?? true,
      showPhoneNumber: json['showPhoneNumber'] as bool? ?? false,
      showEmail: json['showEmail'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'language': language,
      'theme': theme,
      'notificationsEnabled': notificationsEnabled,
      'emailNotifications': emailNotifications,
      'smsNotifications': smsNotifications,
      'pushNotifications': pushNotifications,
      'locationEnabled': locationEnabled,
      'showOnlineStatus': showOnlineStatus,
      'showPhoneNumber': showPhoneNumber,
      'showEmail': showEmail,
    };
  }

  UserSettings copyWith({
    String? language,
    String? currency,
    bool? notificationsEnabled,
    bool? emailNotifications,
    bool? smsNotifications,
    bool? pushNotifications,
    bool? showPhone,
    bool? showEmail,
    bool? showLocation,
    String? theme,
  }) {
    return UserSettings(
      language: language ?? this.language,
      currency: currency ?? this.currency,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      pushNotifications: pushNotifications ?? this.pushNotifications,
      showPhone: showPhone ?? this.showPhone,
      showEmail: showEmail ?? this.showEmail,
      showLocation: showLocation ?? this.showLocation,
      theme: theme ?? this.theme,
    );
  }

  static UserSettings get defaultSettings => const UserSettings(
        language: 'ar',
        currency: 'KWD',
        notificationsEnabled: true,
        emailNotifications: true,
        smsNotifications: false,
        pushNotifications: true,
        showPhone: true,
        showEmail: false,
        showLocation: true,
        theme: 'light',
      );
}
